# ChessNam Database Schema

This document describes the Convex database schema for the ChessNam tournament management application.

## Database Technology

The project uses **Convex** as the database, which is a real-time backend-as-a-service with a document-based database.

## Tables

### 1. `users`

Stores user information from Clerk authentication.

**Fields:**

- `clerkId` (string) - Clerk user ID
- `email` (string) - User email
- `firstName` (optional string) - User's first name
- `lastName` (optional string) - User's last name
- `imageUrl` (optional string) - Profile image URL
- `createdAt` (number) - Timestamp
- `updatedAt` (number) - Timestamp

**Indexes:**

- `by_clerk_id` - For finding users by Clerk ID
- `by_email` - For finding users by email

### 2. `locations`

Stores venue/address information with Google Places data.

**Fields:**

- `formattedAddress` (string) - Human-readable address
- `lat` (number) - Latitude coordinate
- `lng` (number) - Longitude coordinate
- `placeId` (string) - Google Places ID for reference
- `createdAt` (number) - Timestamp
- `updatedAt` (number) - Timestamp

**Indexes:**

- `by_place_id` - For finding locations by Google Places ID
- `by_coordinates` - For finding locations by coordinates

### 3. `tournaments`

Main tournament table storing all tournament information.

**Fields:**

- `name` (string) - Tournament name
- `format` (optional string) - Tournament format (e.g., "Swiss", "Round Robin")
- `description` (optional string) - Tournament description
- `bannerImageId` (optional storage ID) - Banner image
- `timeControlMinutes` (number) - Time control in minutes
- `timeControlIncrement` (number) - Time increment in seconds
- `locationId` (optional ID) - Reference to locations table
- `startDateTime` (number) - Start date/time timestamp
- `endDateTime` (optional number) - End date/time timestamp
- `registrationDeadline` (optional number) - Registration deadline timestamp
- `organizerName` (optional string) - Organizer name
- `organizerEmail` (optional string) - Organizer email
- `organizerPhone` (optional string) - Organizer phone
- `organizerSocialLinks` (optional string) - Organizer social media
- `arbiterName` (optional string) - Chief arbiter name
- `arbiterContact` (optional string) - Chief arbiter contact
- `limitParticipants` (boolean) - Whether to limit participants
- `maxParticipants` (optional number) - Maximum participants
- `isFideRated` (boolean) - Whether tournament is FIDE rated
- `limitByRating` (boolean) - Whether to limit by rating
- `ratingLimit` (optional number) - Rating limit
- `ratingType` (optional "local" | "fide") - Rating type
- `equipment` ("organizer" | "players" | "mixed") - Equipment provision
- `additionalInfo` (optional string) - Additional information
- `status` ("draft" | "published" | "ongoing" | "completed" | "cancelled") - Tournament status
- `createdBy` (user ID) - Creator user ID
- `createdAt` (number) - Creation timestamp
- `updatedAt` (number) - Update timestamp

**Indexes:**

- `by_creator` - For finding tournaments by creator
- `by_status` - For finding tournaments by status
- `by_start_date` - For sorting by start date
- `by_created_at` - For sorting by creation date
- `by_location` - For finding tournaments by location

### 3. `tournament_categories`

Tournament categories with prizes and entry fees.

**Fields:**

- `tournamentId` (tournament ID) - Reference to tournament
- `name` (string) - Category name (e.g., "Open", "Under 18")
- `entryFee` (number) - Entry fee in cents/smallest currency unit
- `prizes` (array) - Prize distribution array with:
  - `place` (number) - Prize place (1st, 2nd, etc.)
  - `amount` (number) - Prize amount in cents/smallest currency unit
- `createdAt` (number) - Creation timestamp
- `updatedAt` (number) - Update timestamp

**Indexes:**

- `by_tournament` - For finding categories by tournament

### 4. `tournament_sponsors`

Tournament sponsor information.

**Fields:**

- `tournamentId` (tournament ID) - Reference to tournament
- `name` (string) - Sponsor name
- `logoImageId` (optional storage ID) - Sponsor logo
- `website` (optional string) - Sponsor website URL
- `createdAt` (number) - Creation timestamp
- `updatedAt` (number) - Update timestamp

**Indexes:**

- `by_tournament` - For finding sponsors by tournament

### 5. `tournament_participants`

Tournament participants (for future use).

**Fields:**

- `tournamentId` (tournament ID) - Reference to tournament
- `categoryId` (category ID) - Reference to category
- `userId` (user ID) - Reference to user
- `playerName` (string) - Player name
- `rating` (optional number) - Player rating
- `ratingType` (optional "local" | "fide") - Rating type
- `registeredAt` (number) - Registration timestamp
- `paymentStatus` ("pending" | "paid" | "refunded") - Payment status
- `status` ("registered" | "confirmed" | "withdrawn" | "disqualified") - Participant status
- `createdAt` (number) - Creation timestamp
- `updatedAt` (number) - Update timestamp

**Indexes:**

- `by_tournament` - For finding participants by tournament
- `by_category` - For finding participants by category
- `by_user` - For finding tournaments by user
- `by_tournament_user` - For finding specific user in tournament

## Available Functions

### Tournament Functions (`convex/tournaments.ts`)

- `createDraftTournament` - Create a new draft tournament with minimal data
- `createTournament` - Create a new tournament with categories and sponsors (legacy)
- `updateTournamentBasics` - Update basic tournament information
- `updateTournamentSchedule` - Update tournament schedule
- `updateTournamentOrganizers` - Update organizer and arbiter information
- `updateTournamentSettings` - Update tournament settings
- `updateTournamentCategories` - Update tournament categories
- `updateTournamentSponsors` - Update tournament sponsors
- `getUserTournaments` - Get tournaments created by current user
- `getTournament` - Get single tournament with categories, sponsors, and location
- `getPublishedTournaments` - Get all published tournaments (public)
- `updateTournamentStatus` - Update tournament status
- `deleteTournament` - Delete tournament and related data

### Location Functions (`convex/tournaments.ts`)

- `createOrFindLocation` - Create or find a location by Google Places data
- `updateTournamentLocation` - Update tournament location reference
- `getLocation` - Get location by ID

### User Functions (`convex/users.ts`)

- `createOrUpdateUser` - Create or update user from Clerk
- `getCurrentUser` - Get current authenticated user
- `getUserById` - Get user by ID

### File Functions (`convex/files.ts`)

- `generateUploadUrl` - Generate URL for file uploads
- `deleteFile` - Delete file from storage
- `getFileUrl` - Get file URL from storage

## Usage Examples

### Creating a Tournament

```typescript
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

const createTournament = useMutation(api.tournaments.createTournament);

// Example usage
await createTournament({
  name: 'Spring Chess Championship',
  format: 'Swiss',
  description: 'Annual spring tournament',
  timeControlMinutes: 10,
  timeControlIncrement: 0,
  startDateTime: Date.now() + 86400000, // Tomorrow
  limitParticipants: true,
  maxParticipants: 50,
  isFideRated: false,
  limitByRating: false,
  equipment: 'organizer',
  categories: [
    {
      name: 'Open',
      entryFee: 50000, // ₱500.00 in cents
      prizes: [
        { place: 1, amount: 1000000 }, // ₱10,000.00
        { place: 2, amount: 500000 }, // ₱5,000.00
        { place: 3, amount: 250000 }, // ₱2,500.00
      ],
    },
  ],
  sponsors: [
    {
      name: 'Chess Store Manila',
      website: 'https://chessstoremanila.com',
    },
  ],
});
```

### Getting User Tournaments

```typescript
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

const tournaments = useQuery(api.tournaments.getUserTournaments);
```

## Data Types

All monetary values (entry fees, prize amounts) are stored as integers in the smallest currency unit (cents for PHP). This prevents floating-point precision issues.

All timestamps are stored as Unix timestamps (milliseconds since epoch) using `Date.now()`.

## Security

- All mutations require authentication via Clerk
- Users can only modify tournaments they created
- File uploads are restricted to authenticated users
- Proper authorization checks are in place for all operations
