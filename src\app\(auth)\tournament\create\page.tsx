'use client';

import { DateTimePicker } from '@/components/common/date-time-picker';
import { FileUpload } from '@/components/common/file-upload';
import { Input } from '@/components/common/input';
import { LocationField } from '@/components/common/location-field';
import { Textarea } from '@/components/common/textarea';
import { TimeControl } from '@/components/common/time-control';
import { Combobox } from '@/components/common/combobox';
import { Header } from '@/components/header';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { CategoryManager } from '@/components/tournament/category-manager';
import { useTournamentForm } from '@/hooks/use-tournament-form';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Id } from '@convex/_generated/dataModel';
import {
  Plus,
  Trophy,
  Settings,
  Users,
  Star,
  Shield,
  Mail,
  Phone,
  Globe,
  Link,
  Building,
  Trash2,
  Calendar,
} from 'lucide-react';

interface CreateTournamentProps {
  tournamentId?: Id<'tournaments'>;
}

const CreateTournament = ({ tournamentId }: CreateTournamentProps = {}) => {
  const router = useRouter();

  // Use the tournament form hook for state management
  const {
    formState,
    setFormState,
    currentTournamentId,
    isLoading,
    isSaving,
    lastSaved,
    autoSave,
    saveField,
    saveSchedule,
    saveSettings,
    saveBannerImage,
    saveTimeControl,
    saveLocation,
    publishTournament,
    createDraft,
  } = useTournamentForm(tournamentId);

  // Redirect to edit page after tournament is created
  useEffect(() => {
    if (
      currentTournamentId &&
      window.location.pathname === '/create-tournament'
    ) {
      window.history.replaceState(
        null,
        '',
        `/tournament/edit/${currentTournamentId}`,
      );
    }
  }, [currentTournamentId]);

  // Helper function to update form state
  const updateFormState = (updates: Partial<typeof formState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  };

  // Sponsor management functions
  const addSponsor = () => {
    const newSponsor = {
      id: `sponsor-${Date.now()}`,
      name: '',
      logo: null,
      website: '',
    };
    updateFormState({
      sponsors: [...formState.sponsors, newSponsor],
    });
  };

  const updateSponsor = (
    id: string,
    field: string,
    value: string | File | null,
  ) => {
    updateFormState({
      sponsors: formState.sponsors.map((sponsor) =>
        sponsor.id === id ? { ...sponsor, [field]: value } : sponsor,
      ),
    });
  };

  const removeSponsor = (id: string) => {
    updateFormState({
      sponsors: formState.sponsors.filter((sponsor) => sponsor.id !== id),
    });
  };

  // Rating type options for combobox
  const ratingTypeOptions = [
    { value: 'local', label: 'Local rating' },
    { value: 'fide', label: 'FIDE rating' },
  ];

  return (
    <>
      <Header />
      <main className="container mx-auto max-w-5xl py-8">
        <h1 className="text-3xl font-bold mb-4">
          {tournamentId ? 'Edit Tournament' : 'Create a new tournament'}
        </h1>
        <div className="space-y-4">
          <Input
            label="Tournament Name"
            value={formState.tournamentName}
            onChange={(e) =>
              updateFormState({ tournamentName: e.target.value })
            }
            onBlur={() => saveField('basic')}
          />

          <Input
            label="Tournament Format"
            value={formState.tournamentFormat}
            onChange={(e) =>
              updateFormState({ tournamentFormat: e.target.value })
            }
            onBlur={() => saveField('basic')}
          />

          <TimeControl
            value={formState.timeControl}
            onValueChange={(timeControl) => {
              updateFormState({ timeControl });
              saveTimeControl(timeControl);
            }}
          />

          <LocationField
            label="Add Location"
            value={formState.location}
            onChange={(location) => updateFormState({ location })}
            onLocationSelect={(locationData) => {
              if (locationData.placeId && locationData.placeId !== 'fallback') {
                saveLocation({
                  formattedAddress: locationData.address,
                  lat: locationData.coordinates.lat,
                  lng: locationData.coordinates.lng,
                  placeId: locationData.placeId,
                });
              }
            }}
          />

          <Textarea
            label="About the Tournament"
            value={formState.description}
            onChange={(e) => updateFormState({ description: e.target.value })}
            onBlur={() => saveField('basic')}
            maxLength={500}
            showCount={true}
            minRows={3}
            maxRows={6}
          />

          <Accordion type="single" collapsible className="w-full space-y-4">
            <AccordionItem
              value="schedules"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">Schedules</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Tournament Schedule
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Set the dates and times for your tournament
                    </p>
                  </div>

                  <div className="space-y-4">
                    <DateTimePicker
                      value={formState.startDateTime}
                      onChange={(startDateTime) => {
                        updateFormState({ startDateTime });
                        saveSchedule({
                          startDateTime,
                          endDateTime: formState.endDateTime,
                          registrationDeadline: formState.registrationDeadline,
                        });
                      }}
                      dateLabel="Start Date"
                      timeLabel="Start Time"
                      timeStep={15}
                      disablePastDates={true}
                    />

                    {!formState.showEndDateTime && (
                      <button
                        type="button"
                        onClick={() =>
                          updateFormState({ showEndDateTime: true })
                        }
                        className="flex items-center gap-1 text-primary hover:text-primary/95 text-sm font-medium transition-colors cursor-pointer"
                      >
                        <Plus className="h-4 w-4" />
                        End date and time
                      </button>
                    )}

                    {formState.showEndDateTime && (
                      <DateTimePicker
                        value={formState.endDateTime}
                        onChange={(endDateTime) => {
                          updateFormState({ endDateTime });
                          saveSchedule({
                            startDateTime: formState.startDateTime,
                            endDateTime,
                            registrationDeadline:
                              formState.registrationDeadline,
                          });
                        }}
                        dateLabel="End Date"
                        timeLabel="End Time"
                        timeStep={15}
                        disablePastDates={true}
                      />
                    )}

                    <DateTimePicker
                      value={formState.registrationDeadline}
                      onChange={(registrationDeadline) => {
                        updateFormState({ registrationDeadline });
                        saveSchedule({
                          startDateTime: formState.startDateTime,
                          endDateTime: formState.endDateTime,
                          registrationDeadline,
                        });
                      }}
                      dateLabel="Registration Deadline"
                      timeLabel="Deadline Time"
                      timeStep={15}
                      disablePastDates={true}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="category-prizes-fees"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Trophy className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">
                    Category Prizes & Fees
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Prize Structure & Entry Fees
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Define tournament categories, prize distribution, and
                      entry fees
                    </p>
                  </div>

                  <CategoryManager
                    categories={formState.categories}
                    onCategoriesChange={(categories) =>
                      updateFormState({ categories })
                    }
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="organizers"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">
                    Organizers & Officials
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Tournament Team
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Add organizer details, officials, and sponsor information
                    </p>
                  </div>

                  {/* Tournament Organizer Section */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-foreground">
                      Tournament Organizer
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label="Organizer Name"
                        value={formState.organizerName}
                        onChange={(e) =>
                          updateFormState({ organizerName: e.target.value })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Users className="h-4 w-4" />}
                      />
                      <Input
                        label="Email Address"
                        type="email"
                        value={formState.organizerEmail}
                        onChange={(e) =>
                          updateFormState({ organizerEmail: e.target.value })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Mail className="h-4 w-4" />}
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label="Contact Number"
                        type="tel"
                        value={formState.organizerPhone}
                        onChange={(e) =>
                          updateFormState({ organizerPhone: e.target.value })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Phone className="h-4 w-4" />}
                      />
                      <Input
                        label="Social Media Links"
                        value={formState.organizerSocialLinks}
                        onChange={(e) =>
                          updateFormState({
                            organizerSocialLinks: e.target.value,
                          })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Globe className="h-4 w-4" />}
                      />
                    </div>
                  </div>

                  {/* Chief Arbiter Section */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-foreground">
                      Chief Arbiter
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label="Arbiter Name"
                        value={formState.arbiterName}
                        onChange={(e) =>
                          updateFormState({ arbiterName: e.target.value })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Shield className="h-4 w-4" />}
                      />
                      <Input
                        label="Contact Information"
                        value={formState.arbiterContact}
                        onChange={(e) =>
                          updateFormState({ arbiterContact: e.target.value })
                        }
                        onBlur={() => saveField('organizers')}
                        icon={<Phone className="h-4 w-4" />}
                      />
                    </div>
                  </div>

                  {/* Sponsors Section */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-foreground">
                      Sponsors
                    </h4>
                    {formState.sponsors.length > 0 && (
                      <div className="space-y-4">
                        {formState.sponsors.map((sponsor) => (
                          <div
                            key={sponsor.id}
                            className="border border-border rounded-lg p-4 space-y-4"
                          >
                            <div className="flex items-center justify-between">
                              <h5 className="text-sm font-medium">
                                Sponsor{' '}
                                {formState.sponsors.indexOf(sponsor) + 1}
                              </h5>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeSponsor(sponsor.id)}
                                className="text-destructive/50 hover:text-destructive hover:bg-destructive/10 h-6 w-6 p-0"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>

                            {/* Desktop: Two columns, Mobile: Single column */}
                            <div className="flex flex-col md:grid md:grid-cols-2 gap-4">
                              {/* Column 1: Image (Desktop) / First item (Mobile) */}
                              <div className="order-1 md:order-1">
                                <FileUpload
                                  onFilesChange={(files) =>
                                    updateSponsor(
                                      sponsor.id,
                                      'logo',
                                      files[0] || null,
                                    )
                                  }
                                  accept="image/*"
                                  multiple={false}
                                  maxFiles={1}
                                  variant="default"
                                />
                              </div>

                              {/* Column 2: Name and Website (Desktop) / Second and third items (Mobile) */}
                              <div className="order-2 md:order-2 space-y-4">
                                <Input
                                  label="Sponsor Name"
                                  value={sponsor.name}
                                  onChange={(e) =>
                                    updateSponsor(
                                      sponsor.id,
                                      'name',
                                      e.target.value,
                                    )
                                  }
                                  onBlur={() => saveField('organizers')}
                                  icon={<Building className="h-4 w-4" />}
                                />
                                <Input
                                  label="Website URL"
                                  type="url"
                                  value={sponsor.website}
                                  onChange={(e) =>
                                    updateSponsor(
                                      sponsor.id,
                                      'website',
                                      e.target.value,
                                    )
                                  }
                                  onBlur={() => saveField('organizers')}
                                  icon={<Link className="h-4 w-4" />}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="flex justify-end">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addSponsor}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Sponsor
                      </Button>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="settings"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Settings className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">Settings</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Tournament Configuration
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Configure participant limits, ratings, equipment, and
                      additional rules
                    </p>
                  </div>

                  {/* Participant Limit Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label
                        htmlFor="limit-participants"
                        className="text-sm font-medium"
                      >
                        Limit number of participants
                      </Label>
                      <Switch
                        id="limit-participants"
                        checked={formState.limitParticipants}
                        onCheckedChange={(limitParticipants) => {
                          updateFormState({ limitParticipants });
                          saveSettings({ limitParticipants });
                        }}
                      />
                    </div>
                    {formState.limitParticipants && (
                      <Input
                        label="Maximum participants"
                        type="number"
                        value={formState.maxParticipants}
                        onChange={(e) =>
                          updateFormState({ maxParticipants: e.target.value })
                        }
                        onBlur={() => saveField('settings')}
                        min="1"
                        step="1"
                        icon={<Users className="h-4 w-4" />}
                      />
                    )}
                  </div>

                  {/* FIDE Rating Section */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="fide-rated" className="text-sm font-medium">
                      FIDE rated tournament
                    </Label>
                    <Switch
                      id="fide-rated"
                      checked={formState.isFideRated}
                      onCheckedChange={(isFideRated) => {
                        updateFormState({ isFideRated });
                        saveSettings({ isFideRated });
                      }}
                    />
                  </div>

                  {/* Rating Restriction Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label
                        htmlFor="limit-rating"
                        className="text-sm font-medium"
                      >
                        Limit by rating
                      </Label>
                      <Switch
                        id="limit-rating"
                        checked={formState.limitByRating}
                        onCheckedChange={(limitByRating) => {
                          updateFormState({ limitByRating });
                          saveSettings({ limitByRating });
                        }}
                      />
                    </div>
                    {formState.limitByRating && (
                      <div className="space-y-4">
                        <Input
                          label="Rating limit"
                          type="number"
                          value={formState.ratingLimit}
                          onChange={(e) =>
                            updateFormState({ ratingLimit: e.target.value })
                          }
                          onBlur={() => saveField('settings')}
                          min="0"
                          step="1"
                          icon={<Star className="h-4 w-4" />}
                        />
                        <Combobox
                          value={formState.ratingType}
                          onValueChange={(ratingType) => {
                            updateFormState({ ratingType });
                            saveField('settings');
                          }}
                          options={ratingTypeOptions}
                          label="Rating type"
                          searchable={false}
                          icon={<Star className="h-4 w-4" />}
                          iconPosition="right"
                        />
                      </div>
                    )}
                  </div>

                  {/* Equipment Section */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Equipment</Label>
                    <RadioGroup
                      value={formState.equipment}
                      onValueChange={(equipment) => {
                        updateFormState({ equipment });
                        saveField('settings');
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="organizer"
                          id="equipment-organizer"
                        />
                        <Label
                          htmlFor="equipment-organizer"
                          className="text-sm font-normal cursor-pointer"
                        >
                          Equipment provided by organizer
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="players"
                          id="equipment-players"
                        />
                        <Label
                          htmlFor="equipment-players"
                          className="text-sm font-normal cursor-pointer"
                        >
                          Players bring their own equipment
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="mixed" id="equipment-mixed" />
                        <Label
                          htmlFor="equipment-mixed"
                          className="text-sm font-normal cursor-pointer"
                        >
                          Mixed (specify in notes)
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Additional Information Section */}
                  <Textarea
                    label="Additional information for participants"
                    value={formState.additionalInfo}
                    onChange={(e) =>
                      updateFormState({ additionalInfo: e.target.value })
                    }
                    onBlur={() => saveField('settings')}
                    maxLength={1000}
                    showCount={true}
                    minRows={3}
                    maxRows={6}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="space-y-4">
            {/* Auto-save indicator */}
            <div className="text-center text-sm text-muted-foreground">
              {isSaving && 'Saving...'}
              {lastSaved &&
                !isSaving &&
                `Last saved: ${lastSaved.toLocaleTimeString()}`}
              {currentTournamentId && !lastSaved && 'Draft created'}
            </div>

            <div className="flex gap-4">
              <Button
                variant="outline"
                className="flex-1 py-8 text-lg font-semibold"
                size="lg"
                onClick={autoSave}
                disabled={isSaving}
              >
                Save Draft
              </Button>
              <Button
                className="flex-1 py-8 text-lg font-semibold"
                size="lg"
                onClick={async () => {
                  const success = await publishTournament();
                  if (success) {
                    router.push('/dashboard');
                  }
                }}
                disabled={isLoading || isSaving}
              >
                {isLoading ? 'Publishing...' : 'Create Tournament'}
              </Button>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default CreateTournament;
