'use client';

import { useState, useEffect, useCallback } from 'react';
import { useTournaments, useTournament } from './use-tournaments';
import { useToast } from './use-toast';
import { Id } from '@convex/_generated/dataModel';
import { type TournamentCategoryData } from '@/components/tournament/tournament-category';
import { type TimeControlValue } from '@/components/common/time-control';

interface TournamentFormState {
  // Basic info
  tournamentName: string;
  tournamentFormat: string;
  description: string;
  bannerImageId?: string;

  // Time control
  timeControl: TimeControlValue;

  // Location
  location: string; // Display value for the location field
  locationId?: string; // ID of the location in the database
  locationData?: {
    formattedAddress: string;
    lat: number;
    lng: number;
    placeId: string;
  };

  // Schedule
  startDateTime?: Date;
  endDateTime?: Date;
  showEndDateTime: boolean;
  registrationDeadline?: Date;

  // Organizer
  organizerName: string;
  organizerEmail: string;
  organizerPhone: string;
  organizerSocialLinks: string;

  // Arbiter
  arbiterName: string;
  arbiterContact: string;

  // Settings
  limitParticipants: boolean;
  maxParticipants: string;
  isFideRated: boolean;
  limitByRating: boolean;
  ratingLimit: string;
  ratingType: string;
  equipment: string;
  additionalInfo: string;

  // Categories
  categories: TournamentCategoryData[];

  // Sponsors
  sponsors: Array<{
    id: string;
    name: string;
    logo: File | null;
    logoImageId?: string;
    website: string;
  }>;
}

const initialFormState: TournamentFormState = {
  tournamentName: '',
  tournamentFormat: '',
  description: '',
  timeControl: { time: '10', increment: '0' },
  location: '',
  showEndDateTime: false,
  organizerName: '',
  organizerEmail: '',
  organizerPhone: '',
  organizerSocialLinks: '',
  arbiterName: '',
  arbiterContact: '',
  limitParticipants: false,
  maxParticipants: '',
  isFideRated: false,
  limitByRating: false,
  ratingLimit: '',
  ratingType: 'local',
  equipment: 'organizer',
  additionalInfo: '',
  categories: [
    {
      id: 'category-1',
      name: '',
      entryFee: '',
      prizes: [
        { place: 1, amount: '' },
        { place: 2, amount: '' },
        { place: 3, amount: '' },
      ],
    },
  ],
  sponsors: [],
};

export function useTournamentForm(tournamentId?: Id<'tournaments'>) {
  const [formState, setFormState] =
    useState<TournamentFormState>(initialFormState);
  const [currentTournamentId, setCurrentTournamentId] = useState<
    Id<'tournaments'> | undefined
  >(tournamentId);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  const { toast } = useToast();
  const {
    createDraftTournament,
    updateTournamentBasics,
    updateTournamentSchedule,
    updateTournamentOrganizers,
    updateTournamentSettings,
    updateTournamentCategories,
    updateTournamentSponsors,
    updateTournamentStatus,
    createOrFindLocation,
    updateTournamentLocation,
  } = useTournaments();

  const tournament = useTournament(currentTournamentId);

  // Load tournament data when tournament is fetched
  useEffect(() => {
    if (tournament && !isLoading && !isSaving) {
      // Only update form state if this is the first load or if we haven't initialized the form yet
      if (!isFormInitialized) {
        setFormState({
          tournamentName: tournament.name || '',
          tournamentFormat: tournament.format || '',
          description: tournament.description || '',
          bannerImageId: tournament.bannerImageId,
          timeControl: {
            time: tournament.timeControlMinutes?.toString() || '10',
            increment: tournament.timeControlIncrement?.toString() || '0',
          },
          location: tournament.location?.formattedAddress || '',
          locationId: tournament.locationId,
          locationData: tournament.location
            ? {
                formattedAddress: tournament.location.formattedAddress,
                lat: tournament.location.lat,
                lng: tournament.location.lng,
                placeId: tournament.location.placeId,
              }
            : undefined,
          startDateTime: tournament.startDateTime
            ? new Date(tournament.startDateTime)
            : undefined,
          endDateTime: tournament.endDateTime
            ? new Date(tournament.endDateTime)
            : undefined,
          showEndDateTime: !!tournament.endDateTime,
          registrationDeadline: tournament.registrationDeadline
            ? new Date(tournament.registrationDeadline)
            : undefined,
          organizerName: tournament.organizerName || '',
          organizerEmail: tournament.organizerEmail || '',
          organizerPhone: tournament.organizerPhone || '',
          organizerSocialLinks: tournament.organizerSocialLinks || '',
          arbiterName: tournament.arbiterName || '',
          arbiterContact: tournament.arbiterContact || '',
          limitParticipants: tournament.limitParticipants ?? false,
          maxParticipants: tournament.maxParticipants?.toString() || '',
          isFideRated: tournament.isFideRated ?? false,
          limitByRating: tournament.limitByRating ?? false,
          ratingLimit: tournament.ratingLimit?.toString() || '',
          ratingType: tournament.ratingType || 'local',
          equipment: tournament.equipment || 'organizer',
          additionalInfo: tournament.additionalInfo || '',
          categories:
            tournament.categories?.map((cat: any) => ({
              id: cat._id,
              name: cat.name,
              entryFee: (cat.entryFee / 100).toString(), // Convert from cents
              prizes: cat.prizes.map((prize: any) => ({
                place: prize.place,
                amount: (prize.amount / 100).toString(), // Convert from cents
              })),
            })) || [],
          sponsors:
            tournament.sponsors?.map((sponsor: any) => ({
              id: sponsor._id,
              name: sponsor.name,
              logo: null,
              logoImageId: sponsor.logoImageId,
              website: sponsor.website || '',
            })) || [],
        });
        setIsFormInitialized(true);
      }
    }
  }, [tournament, isLoading, isSaving, isFormInitialized]);

  // Reset form initialization when tournament ID changes
  useEffect(() => {
    setIsFormInitialized(false);
  }, [currentTournamentId]);

  // Create draft tournament if none exists
  const createDraft = useCallback(async () => {
    if (currentTournamentId) return currentTournamentId;

    try {
      setIsLoading(true);
      const newTournamentId = await createDraftTournament({
        name: formState.tournamentName || undefined,
      });
      setCurrentTournamentId(newTournamentId);
      return newTournamentId;
    } catch (error) {
      console.error('Error creating draft tournament:', error);
      toast({
        title: 'Error',
        description: 'Failed to create draft tournament',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [
    currentTournamentId,
    formState.tournamentName,
    createDraftTournament,
    toast,
  ]);

  // Auto-save function with debouncing
  const autoSave = useCallback(async () => {
    const tournamentId = currentTournamentId || (await createDraft());
    if (!tournamentId) return;

    try {
      setIsSaving(true);

      // Save basic info
      await updateTournamentBasics({
        tournamentId,
        name: formState.tournamentName || undefined,
        format: formState.tournamentFormat || undefined,
        description: formState.description || undefined,
        bannerImageId: formState.bannerImageId as Id<'_storage'> | undefined,
        timeControlMinutes: parseInt(formState.timeControl.time) || undefined,
        timeControlIncrement:
          parseInt(formState.timeControl.increment) || undefined,
        locationId: formState.locationId as Id<'locations'> | undefined,
      });

      // Save schedule
      await updateTournamentSchedule({
        tournamentId,
        startDateTime: formState.startDateTime?.getTime(),
        endDateTime: formState.endDateTime?.getTime(),
        registrationDeadline: formState.registrationDeadline?.getTime(),
      });

      // Save organizers
      await updateTournamentOrganizers({
        tournamentId,
        organizerName: formState.organizerName || undefined,
        organizerEmail: formState.organizerEmail || undefined,
        organizerPhone: formState.organizerPhone || undefined,
        organizerSocialLinks: formState.organizerSocialLinks || undefined,
        arbiterName: formState.arbiterName || undefined,
        arbiterContact: formState.arbiterContact || undefined,
      });

      // Save settings
      await updateTournamentSettings({
        tournamentId,
        limitParticipants: formState.limitParticipants,
        maxParticipants: formState.limitParticipants
          ? parseInt(formState.maxParticipants) || undefined
          : undefined,
        isFideRated: formState.isFideRated,
        limitByRating: formState.limitByRating,
        ratingLimit: formState.limitByRating
          ? parseInt(formState.ratingLimit) || undefined
          : undefined,
        ratingType: formState.limitByRating
          ? (formState.ratingType as 'local' | 'fide')
          : undefined,
        equipment: formState.equipment as 'organizer' | 'players' | 'mixed',
        additionalInfo: formState.additionalInfo || undefined,
      });

      // Save categories
      const categoriesData = formState.categories
        .filter((cat) => cat.name.trim() !== '')
        .map((cat) => ({
          id: cat.id.startsWith('category-')
            ? undefined
            : (cat.id as Id<'tournament_categories'>),
          name: cat.name,
          entryFee: Math.round((parseFloat(cat.entryFee) || 0) * 100), // Convert to cents
          prizes: cat.prizes.map((prize) => ({
            place: prize.place,
            amount: Math.round((parseFloat(prize.amount) || 0) * 100), // Convert to cents
          })),
        }));

      if (categoriesData.length > 0) {
        await updateTournamentCategories({
          tournamentId,
          categories: categoriesData,
        });
      }

      // Save sponsors
      const sponsorsData = formState.sponsors
        .filter((sponsor) => sponsor.name.trim() !== '')
        .map((sponsor) => ({
          id: sponsor.id.startsWith('sponsor-')
            ? undefined
            : (sponsor.id as Id<'tournament_sponsors'>),
          name: sponsor.name,
          logoImageId: sponsor.logoImageId as Id<'_storage'> | undefined,
          website: sponsor.website || undefined,
        }));

      if (sponsorsData.length > 0) {
        await updateTournamentSponsors({
          tournamentId,
          sponsors: sponsorsData,
        });
      }

      setLastSaved(new Date());
    } catch (error) {
      console.error('Error auto-saving tournament:', error);
    } finally {
      setIsSaving(false);
    }
  }, [
    currentTournamentId,
    formState,
    createDraft,
    updateTournamentBasics,
    updateTournamentSchedule,
    updateTournamentOrganizers,
    updateTournamentSettings,
    updateTournamentCategories,
    updateTournamentSponsors,
  ]);

  // Publish tournament
  const publishTournament = useCallback(async () => {
    const tournamentId = currentTournamentId || (await createDraft());
    if (!tournamentId) return false;

    try {
      // Save all data first
      await autoSave();

      // Then publish
      await updateTournamentStatus({
        tournamentId,
        status: 'published',
      });

      toast({
        title: 'Tournament Published',
        description: 'Your tournament has been published successfully!',
      });

      return true;
    } catch (error) {
      console.error('Error publishing tournament:', error);
      toast({
        title: 'Error',
        description: 'Failed to publish tournament',
        variant: 'destructive',
      });
      return false;
    }
  }, [
    currentTournamentId,
    createDraft,
    autoSave,
    updateTournamentStatus,
    toast,
  ]);

  // Save specific field when user finishes editing
  const saveField = useCallback(
    async (fieldType: 'basic' | 'schedule' | 'organizers' | 'settings') => {
      const tournamentId = currentTournamentId || (await createDraft());
      if (!tournamentId) return;

      try {
        setIsSaving(true);

        switch (fieldType) {
          case 'basic':
            await updateTournamentBasics({
              tournamentId,
              name: formState.tournamentName || undefined,
              format: formState.tournamentFormat || undefined,
              description: formState.description || undefined,
              bannerImageId: formState.bannerImageId as
                | Id<'_storage'>
                | undefined,
              timeControlMinutes:
                parseInt(formState.timeControl.time) || undefined,
              timeControlIncrement:
                parseInt(formState.timeControl.increment) || undefined,
              locationId: formState.locationId as Id<'locations'> | undefined,
            });
            break;
          case 'schedule':
            await updateTournamentSchedule({
              tournamentId,
              startDateTime: formState.startDateTime?.getTime(),
              endDateTime: formState.endDateTime?.getTime(),
              registrationDeadline: formState.registrationDeadline?.getTime(),
            });
            break;
          case 'organizers':
            await updateTournamentOrganizers({
              tournamentId,
              organizerName: formState.organizerName || undefined,
              organizerEmail: formState.organizerEmail || undefined,
              organizerPhone: formState.organizerPhone || undefined,
              organizerSocialLinks: formState.organizerSocialLinks || undefined,
              arbiterName: formState.arbiterName || undefined,
              arbiterContact: formState.arbiterContact || undefined,
            });
            break;
          case 'settings':
            await updateTournamentSettings({
              tournamentId,
              limitParticipants: formState.limitParticipants,
              maxParticipants: formState.limitParticipants
                ? parseInt(formState.maxParticipants) || undefined
                : undefined,
              isFideRated: formState.isFideRated,
              limitByRating: formState.limitByRating,
              ratingLimit: formState.limitByRating
                ? parseInt(formState.ratingLimit) || undefined
                : undefined,
              ratingType: formState.limitByRating
                ? (formState.ratingType as 'local' | 'fide')
                : undefined,
              equipment: formState.equipment as
                | 'organizer'
                | 'players'
                | 'mixed',
              additionalInfo: formState.additionalInfo || undefined,
            });
            break;
        }

        setLastSaved(new Date());
      } catch (error) {
        console.error('Error saving field:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [
      currentTournamentId,
      formState,
      createDraft,
      updateTournamentBasics,
      updateTournamentSchedule,
      updateTournamentOrganizers,
      updateTournamentSettings,
    ],
  );

  // Save schedule with specific values (to handle timing issues with state updates)
  const saveSchedule = useCallback(
    async (scheduleData: {
      startDateTime?: Date;
      endDateTime?: Date;
      registrationDeadline?: Date;
    }) => {
      const tournamentId = currentTournamentId || (await createDraft());
      if (!tournamentId) return;

      try {
        setIsSaving(true);
        await updateTournamentSchedule({
          tournamentId,
          startDateTime: scheduleData.startDateTime?.getTime(),
          endDateTime: scheduleData.endDateTime?.getTime(),
          registrationDeadline: scheduleData.registrationDeadline?.getTime(),
        });
        setLastSaved(new Date());
      } catch (error) {
        console.error('Error saving schedule:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [currentTournamentId, createDraft, updateTournamentSchedule],
  );

  // Save banner image and create/update tournament
  const saveBannerImage = useCallback(
    async (bannerImageId: string) => {
      const tournamentId = currentTournamentId || (await createDraft());
      if (!tournamentId) return;

      try {
        setIsSaving(true);
        await updateTournamentBasics({
          tournamentId,
          bannerImageId: bannerImageId as Id<'_storage'>,
          name: formState.tournamentName || undefined,
        });
        setLastSaved(new Date());
      } catch (error) {
        console.error('Error saving banner image:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [
      currentTournamentId,
      formState.tournamentName,
      createDraft,
      updateTournamentBasics,
    ],
  );

  // Save time control with specific value
  const saveTimeControl = useCallback(
    async (timeControl: TimeControlValue) => {
      const tournamentId = currentTournamentId || (await createDraft());
      if (!tournamentId) return;

      try {
        setIsSaving(true);
        await updateTournamentBasics({
          tournamentId,
          name: formState.tournamentName || undefined,
          format: formState.tournamentFormat || undefined,
          description: formState.description || undefined,
          bannerImageId: formState.bannerImageId as Id<'_storage'> | undefined,
          timeControlMinutes: parseInt(timeControl.time) || undefined,
          timeControlIncrement: parseInt(timeControl.increment) || undefined,
          locationId: formState.locationId as Id<'locations'> | undefined,
        });
        setLastSaved(new Date());
      } catch (error) {
        console.error('Error saving time control:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [
      currentTournamentId,
      formState.tournamentName,
      formState.tournamentFormat,
      formState.description,
      formState.bannerImageId,
      formState.location,
      createDraft,
      updateTournamentBasics,
    ],
  );

  // Save location data when user selects a location
  const saveLocation = useCallback(
    async (locationData: {
      formattedAddress: string;
      lat: number;
      lng: number;
      placeId: string;
    }) => {
      const tournamentId = currentTournamentId || (await createDraft());
      if (!tournamentId) return;

      try {
        setIsSaving(true);

        // Create or find the location in the database
        const locationId = await createOrFindLocation(locationData);

        // Update the tournament with the location reference
        await updateTournamentLocation({
          tournamentId,
          locationId,
        });

        // Update form state
        setFormState((prev) => ({
          ...prev,
          location: locationData.formattedAddress,
          locationId,
          locationData,
        }));

        setLastSaved(new Date());
      } catch (error) {
        console.error('Error saving location:', error);
        toast({
          title: 'Error',
          description: 'Failed to save location',
          variant: 'destructive',
        });
      } finally {
        setIsSaving(false);
      }
    },
    [
      currentTournamentId,
      createDraft,
      createOrFindLocation,
      updateTournamentLocation,
      setFormState,
      toast,
    ],
  );

  return {
    formState,
    setFormState,
    currentTournamentId,
    isLoading,
    isSaving,
    lastSaved,
    autoSave,
    saveField,
    saveSchedule,
    saveBannerImage,
    saveTimeControl,
    saveLocation,
    publishTournament,
    createDraft,
  };
}
